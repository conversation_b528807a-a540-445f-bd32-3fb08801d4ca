import QRCode from 'qrcode';

// Generate QR code data URL for a desk
export async function generateDeskQRCode(deskId: string, deskName: string, baseUrl: string): Promise<string> {
  // Create a direct URL to the check-in page with desk ID embedded
  const checkInUrl = `${baseUrl}/desk-checkin/${deskId}`;

  // Use high error correction level since we'll be adding content in the center
  return await QRCode.toDataURL(checkInUrl, {
    errorCorrectionLevel: 'H', // High error correction (30% recovery)
    type: 'image/png',
    quality: 0.92,
    margin: 1,
    color: {
      dark: '#000000',
      light: '#FFFFFF'
    },
    width: 300 // Fixed size for consistent overlay
  });
}