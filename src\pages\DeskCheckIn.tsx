import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { LogIn, Calendar, MapPin } from 'lucide-react';
import { format } from 'date-fns';
import { Skeleton } from '@/components/ui/skeleton';

export function DeskCheckIn() {
  const { deskId } = useParams();
  const navigate = useNavigate();
  const { appUser, isLoading: authLoading } = useAuth();
  const queryClient = useQueryClient();
  const [desk, setDesk] = useState<any>(null);
  const [booking, setBooking] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isChecking, setIsChecking] = useState(false);

  useEffect(() => {
    const fetchDeskAndBooking = async () => {
      if (!deskId || authLoading) return;
      
      try {
        // Fetch desk details
        const { data: deskData, error: deskError } = await supabase
          .from('desks')
          .select(`
            *,
            zone:zones (*)
          `)
          .eq('id', deskId)
          .single();
        
        if (deskError) throw deskError;
        setDesk(deskData);
        
        if (appUser) {
          // Fetch user's booking for this desk today
          const today = new Date().toISOString().split('T')[0];
          const { data: bookingData, error: bookingError } = await supabase
            .from('bookings')
            .select('*')
            .eq('desk_id', deskId)
            .eq('user_id', appUser.id)
            .eq('status', 'booked')
            .gte('date', today)
            .lte('end_date', today)
            .single();
          
          if (!bookingError) {
            setBooking(bookingData);
          }
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchDeskAndBooking();
  }, [deskId, appUser, authLoading]);

  const handleCheckIn = async () => {
    if (!booking) return;
    
    setIsChecking(true);
    try {
      const { error } = await supabase
        .from('bookings')
        .update({ status: 'checked-in' })
        .eq('id', booking.id);
      
      if (error) throw error;
      
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['user-current-bookings'] });
      queryClient.invalidateQueries({ queryKey: ['upcoming-bookings'] });
      queryClient.invalidateQueries({ queryKey: ['monthly-bookings'] });
      queryClient.invalidateQueries({ queryKey: ['user-bookings'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      
      toast.success('Successfully checked in!');
      
      // Redirect to dashboard after successful check-in
      setTimeout(() => navigate('/dashboard'), 2000);
    } catch (error: any) {
      toast.error('Failed to check in: ' + error.message);
    } finally {
      setIsChecking(false);
    }
  };

  // Handle loading state
  if (authLoading || isLoading) {
    return (
      <div className="container max-w-md mx-auto p-4">
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-3/4 mb-2" />
            <Skeleton className="h-4 w-full" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-20 w-full mb-4" />
            <Skeleton className="h-4 w-3/4 mb-2" />
            <Skeleton className="h-4 w-1/2" />
          </CardContent>
          <CardFooter>
            <Skeleton className="h-10 w-full" />
          </CardFooter>
        </Card>
      </div>
    );
  }

  // Handle not logged in
  if (!appUser) {
    return (
      <div className="container max-w-md mx-auto p-4">
        <Card>
          <CardHeader>
            <CardTitle>Sign In Required</CardTitle>
            <CardDescription>
              Please sign in to check in to this desk
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              You need to be signed in to check in to desk {desk?.name || 'Unknown'}
            </p>
          </CardContent>
          <CardFooter>
            <Button 
              className="w-full" 
              onClick={() => navigate(`/login?redirect=/desk-checkin/${deskId}`)}
            >
              Sign In
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  // Handle no booking found
  if (!booking) {
    return (
      <div className="container max-w-md mx-auto p-4">
        <Card>
          <CardHeader>
            <CardTitle>No Booking Found</CardTitle>
            <CardDescription>
              You don't have a booking for this desk today
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-2 mb-4">
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">
                  {desk?.name || 'Unknown Desk'} ({desk?.zone?.name || 'Unknown Zone'})
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">
                  {format(new Date(), 'EEEE, MMMM d, yyyy')}
                </span>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button 
              className="w-full" 
              onClick={() => navigate('/floor-plan')}
              variant="outline"
            >
              Book a Desk
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  // Show check-in UI
  return (
    <div className="container max-w-md mx-auto p-4">
      <Card>
        <CardHeader>
          <CardTitle>Check In to Your Desk</CardTitle>
          <CardDescription>
            Confirm your arrival at the desk
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-2 mb-4">
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">
                {desk?.name || 'Unknown Desk'} ({desk?.zone?.name || 'Unknown Zone'})
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">
                {format(new Date(booking.date), 'EEEE, MMMM d, yyyy')}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm">
                {booking.start_time} - {booking.end_time}
              </span>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button 
            className="w-full" 
            onClick={handleCheckIn}
            disabled={isChecking}
          >
            {isChecking ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Checking In...
              </>
            ) : (
              <>
                <LogIn className="h-4 w-4 mr-2" />
                Check In Now
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}