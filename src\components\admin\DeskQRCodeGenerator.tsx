import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { generateDeskQRCode } from '@/lib/qrCode';
import { Download, Printer } from 'lucide-react';

interface DeskQRCodeGeneratorProps {
  desk: {
    id: string;
    name: string;
  };
}

export function DeskQRCodeGenerator({ desk }: DeskQRCodeGeneratorProps) {
  const [qrCodeUrl, setQrCodeUrl] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);

  const generateQRCode = async () => {
    setIsGenerating(true);
    try {
      // Use the current window location to build the base URL
      const baseUrl = window.location.origin;
      const dataUrl = await generateDeskQRCode(desk.id, desk.name, baseUrl);
      setQrCodeUrl(dataUrl);
    } catch (error) {
      console.error('Failed to generate QR code:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const downloadQRCode = () => {
    if (!qrCodeUrl) return;
    
    const link = document.createElement('a');
    link.href = qrCodeUrl;
    link.download = `desk-qr-${desk.name}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const printQRCode = () => {
    if (!qrCodeUrl) return;
    
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>Desk QR Code - ${desk.name}</title>
            <style>
              body { display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100vh; }
              img { max-width: 300px; }
              h2 { font-family: Arial, sans-serif; }
            </style>
          </head>
          <body>
            <h2>Desk: ${desk.name}</h2>
            <img src="${qrCodeUrl}" alt="Desk QR Code" />
            <p>Scan to check in</p>
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.focus();
      printWindow.print();
      printWindow.close();
    }
  };

  return (
    <div className="flex flex-col items-center gap-4">
      {!qrCodeUrl ? (
        <Button onClick={generateQRCode} disabled={isGenerating}>
          {isGenerating ? 'Generating...' : 'Generate QR Code'}
        </Button>
      ) : (
        <>
          <div className="border p-4 rounded-lg">
            <img src={qrCodeUrl} alt="Desk QR Code" className="w-48 h-48" />
          </div>
          <div className="flex gap-2">
            <Button size="sm" onClick={downloadQRCode}>
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
            <Button size="sm" variant="outline" onClick={printQRCode}>
              <Printer className="h-4 w-4 mr-2" />
              Print
            </Button>
          </div>
        </>
      )}
    </div>
  );
}